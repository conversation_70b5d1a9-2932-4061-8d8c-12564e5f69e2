name: pour

shape_meta: &shape_meta
  # acceptable types: rgb, low_dim, point_cloud
  obs:
    front_point_cloud:
      shape: [4096, 6]
      type: point_cloud
    agent_pos:
      shape: [32]
      type: low_dim
  action:
    shape: [25]

dataset:
  _target_: diffusion_policy_3d.dataset.gr1_dex_dataset_multicam.GR1DexDatasetMultiCam
  zarr_path: raw_pour_converted  # 使用你的数据路径
  horizon: ${horizon}
  pad_before: ${eval:'${n_obs_steps}-1'}
  pad_after: ${eval:'${n_action_steps}-1'}
  seed: 42
  val_ratio: 0.11   # 约11%作为验证集，确保9个episodes中有1个作为验证集
  max_train_episodes: null     # null表示使用所有可用episode，可通过命令行覆盖
  use_language: ${use_language}
  use_img: ${use_image}
  use_front_cam: ${policy.pointcloud_encoder_cfg.use_front_cam}
  num_points_front: ${policy.pointcloud_encoder_cfg.num_points_front}
  use_right_cam: ${policy.pointcloud_encoder_cfg.use_right_cam}
  num_points_right: ${policy.pointcloud_encoder_cfg.num_points_right}
