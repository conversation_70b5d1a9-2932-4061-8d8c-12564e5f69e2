# H5转zarr
bash convert_data.sh

# 查看数据


cd /home/<USER>/code/company/torqueidp3/Improved-3D-Diffusion-Policy && python vis_dataset.py --dataset_path ../raw_pour_converted --use_img 1 --vis_cloud 1 --use_pc_color 1

# bash 训练
# 使用batch_size=120，数据增强开启
conda activate idp3 && bash scripts/train_policy.sh idp3 gr1_dex-3d pour_cross_attn_add_aug 0 8 0.11 500 120 "pour_cross_attention"



# python 脚本
conda activate idp3 && python Improved-3D-Diffusion-Policy/train.py --config-name=idp3.yaml \
    task=gr1_dex-3d \
    hydra.run.dir=data/outputs/gr1_dex-3d-idp3-full_training_seed0 \
    training.debug=False \
    training.seed=0 \
    training.device="cuda:0" \
    exp_name=gr1_dex-3d-idp3-full_training \
    logging.mode=offline \
    checkpoint.save_ckpt=True \
    task.dataset.zarr_path=/home/<USER>/code/opensource/Improved-3D-Diffusion-Policy/raw_pour_converted \
    task.dataset.max_train_episodes=8 \
    task.dataset.val_ratio=0.11 \
    training.checkpoint_every=50

# 验证（在数据集上评估模型）
 CHECKPOINT_PATH="/home/<USER>/code/company/torqueidp3/Improved-3D-Diffusion-Policy/data/outputs/gr1_dex-3d-idp3-pour_cross_attn_add_aug_seed0/checkpoints/best_ep26000_score-0.000_b120_cross_attention_add_ch256.ckpt" \
 python Improved-3D-Diffusion-Policy/eval_joint_angles.py \
 --config-path=diffusion_policy_3d/config \
 --config-name=idp3 \
 task.dataset.zarr_path=/home/<USER>/code/company/torqueidp3/data/raw_pour_converted
